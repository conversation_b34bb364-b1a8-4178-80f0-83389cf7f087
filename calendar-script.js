class MonthlyCalendar {
    constructor() {
        this.currentDate = new Date();
        this.currentMonth = this.currentDate.getMonth();
        this.currentYear = 2024;
        this.today = new Date();
        
        this.monthNames = [
            'JANUARY', 'FEBRUARY', 'MARCH', 'APRIL', 'MAY', 'JUNE',
            'JULY', 'AUGUST', 'SEPTEMBER', 'OCTOBER', 'NOVEMBER', 'DECEMBER'
        ];
        
        this.monthChinese = [
            '一月', '二月', '三月', '四月', '五月', '六月',
            '七月', '八月', '九月', '十月', '十一月', '十二月'
        ];
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.updateCalendar();
        this.updateActiveMonthButton();
    }
    
    bindEvents() {
        // Navigation buttons
        document.getElementById('prevBtn').addEventListener('click', () => {
            this.previousMonth();
        });
        
        document.getElementById('nextBtn').addEventListener('click', () => {
            this.nextMonth();
        });
        
        // Quick navigation buttons
        document.querySelectorAll('.month-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const month = parseInt(e.target.dataset.month);
                this.goToMonth(month);
            });
        });
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                this.previousMonth();
            } else if (e.key === 'ArrowRight') {
                this.nextMonth();
            }
        });
    }
    
    previousMonth() {
        this.currentMonth--;
        if (this.currentMonth < 0) {
            this.currentMonth = 11;
            this.currentYear--;
        }
        this.updateCalendar();
        this.updateActiveMonthButton();
    }
    
    nextMonth() {
        this.currentMonth++;
        if (this.currentMonth > 11) {
            this.currentMonth = 0;
            this.currentYear++;
        }
        this.updateCalendar();
        this.updateActiveMonthButton();
    }
    
    goToMonth(month) {
        this.currentMonth = month;
        this.updateCalendar();
        this.updateActiveMonthButton();
    }
    
    updateCalendar() {
        this.updateHeader();
        this.generateCalendarDays();
    }
    
    updateHeader() {
        document.getElementById('monthNumber').textContent = this.currentMonth + 1;
        document.getElementById('monthName').textContent = this.monthNames[this.currentMonth];
        document.getElementById('monthChinese').textContent = this.monthChinese[this.currentMonth];
    }
    
    updateActiveMonthButton() {
        document.querySelectorAll('.month-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-month="${this.currentMonth}"]`).classList.add('active');
    }
    
    generateCalendarDays() {
        const calendarDays = document.getElementById('calendarDays');
        calendarDays.innerHTML = '';
        
        const firstDay = new Date(this.currentYear, this.currentMonth, 1);
        const lastDay = new Date(this.currentYear, this.currentMonth + 1, 0);
        const daysInMonth = lastDay.getDate();
        
        // Get first day of week (Monday = 0, Sunday = 6)
        let startDay = firstDay.getDay();
        startDay = startDay === 0 ? 6 : startDay - 1; // Convert Sunday from 0 to 6
        
        // Previous month days
        const prevMonth = this.currentMonth === 0 ? 11 : this.currentMonth - 1;
        const prevYear = this.currentMonth === 0 ? this.currentYear - 1 : this.currentYear;
        const daysInPrevMonth = new Date(prevYear, prevMonth + 1, 0).getDate();
        
        for (let i = startDay - 1; i >= 0; i--) {
            const dayElement = this.createDayElement(
                daysInPrevMonth - i,
                'other-month'
            );
            calendarDays.appendChild(dayElement);
        }
        
        // Current month days
        for (let day = 1; day <= daysInMonth; day++) {
            const dayElement = this.createDayElement(day, 'current-month');
            
            // Check if it's today
            if (this.currentYear === this.today.getFullYear() &&
                this.currentMonth === this.today.getMonth() &&
                day === this.today.getDate()) {
                dayElement.classList.add('today');
            }
            
            calendarDays.appendChild(dayElement);
        }
        
        // Next month days to fill the grid
        const totalCells = calendarDays.children.length;
        const remainingCells = 42 - totalCells; // 6 rows × 7 days
        
        for (let day = 1; day <= remainingCells; day++) {
            const dayElement = this.createDayElement(day, 'other-month');
            calendarDays.appendChild(dayElement);
        }
        
        // Add Sunday class to appropriate days
        this.markSundays();
    }
    
    createDayElement(day, monthClass) {
        const dayElement = document.createElement('div');
        dayElement.className = `day ${monthClass}`;
        dayElement.textContent = day;
        
        dayElement.addEventListener('click', () => {
            // Only allow selection of current month days
            if (monthClass === 'current-month') {
                // Remove previous selection
                document.querySelectorAll('.day.selected').forEach(el => {
                    el.classList.remove('selected');
                });

                // Add selection to clicked day
                dayElement.classList.add('selected');

                // Optional: Log selected date
                console.log(`Selected: ${this.currentYear}-${(this.currentMonth + 1).toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`);
            }
        });
        
        return dayElement;
    }
    
    markSundays() {
        const days = document.querySelectorAll('.day');
        days.forEach((day, index) => {
            if ((index + 1) % 7 === 0) { // Every 7th day is Sunday
                day.classList.add('sunday');
            }
        });
    }
}

// Initialize calendar when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MonthlyCalendar();
});
