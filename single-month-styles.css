* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #f8e8e8 0%, #f0d0d0 100%);
    min-height: 100vh;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.calendar-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 30px;
    max-width: 800px;
    width: 100%;
}

/* Header Navigation */
.calendar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
    padding: 0 10px;
}

.nav-btn {
    background: #dc3545;
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background: #c82333;
    transform: scale(1.1);
}

.month-year-display {
    display: flex;
    align-items: center;
    gap: 20px;
}

.month-number {
    background: #dc3545;
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 24px;
}

.month-info {
    text-align: center;
}

.month-name {
    color: #dc3545;
    font-weight: bold;
    font-size: 28px;
    letter-spacing: 1px;
}

.month-chinese {
    color: #dc3545;
    font-size: 18px;
    margin-top: 5px;
}

.year {
    color: #666;
    font-size: 16px;
    margin-top: 5px;
}

/* Calendar Grid */
.calendar-grid {
    margin-bottom: 30px;
}

.days-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.day-header {
    text-align: center;
    font-weight: bold;
    color: #666;
    padding: 10px;
    font-size: 14px;
}

.day-header.sunday {
    color: #007bff;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 10px;
}

.day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    font-size: 18px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.day:hover {
    background: #f0f0f0;
    transform: scale(1.05);
}

.day.current-month {
    color: #333;
}

.day.other-month {
    color: #ccc;
}

.day.sunday {
    color: #007bff;
    font-weight: bold;
}

.day.today {
    background: #dc3545;
    color: white;
    font-weight: bold;
}

.day.today:hover {
    background: #c82333;
}

.day.selected {
    border: 3px solid #007bff;
    color: #007bff;
    font-weight: bold;
    background: transparent;
}

.day.selected:hover {
    border-color: #0056b3;
    color: #0056b3;
    background: rgba(0, 123, 255, 0.1);
}

.day.selected.today {
    border: 3px solid #007bff;
    background: #dc3545;
    color: white;
}

.day.selected.today:hover {
    border-color: #0056b3;
    background: #c82333;
}

/* Animation for month transitions */
.calendar-days {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Quick Navigation */
.quick-nav {
    border-top: 1px solid #eee;
    padding-top: 20px;
}

.quick-nav-title {
    text-align: center;
    color: #666;
    font-weight: bold;
    margin-bottom: 15px;
}

.month-buttons {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 10px;
}

.month-btn {
    padding: 10px;
    border: 2px solid #ddd;
    background: white;
    color: #666;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.month-btn:hover {
    border-color: #dc3545;
    color: #dc3545;
}

.month-btn.active {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .calendar-container {
        padding: 20px;
        margin: 10px;
    }
    
    .month-name {
        font-size: 24px;
    }
    
    .month-number {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .nav-btn {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
    
    .day {
        font-size: 16px;
    }
    
    .month-buttons {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 480px) {
    .calendar-header {
        flex-direction: column;
        gap: 20px;
    }
    
    .month-year-display {
        order: -1;
    }
    
    .month-buttons {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .day {
        font-size: 14px;
    }
}
