* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f8e8e8;
    padding: 20px;
}

.calendar-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(4, 1fr);
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.month {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.month-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.month-number {
    background-color: #dc3545;
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    margin-right: 10px;
}

.month-name {
    color: #dc3545;
    font-weight: bold;
    font-size: 14px;
    letter-spacing: 0.5px;
}

.days-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    margin-bottom: 5px;
}

.days-header span {
    text-align: center;
    font-size: 10px;
    font-weight: bold;
    color: #666;
    padding: 3px 0;
}

.days-header .sunday {
    color: #007bff;
}

.days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
}

.days span {
    text-align: center;
    padding: 4px 2px;
    font-size: 12px;
    font-weight: 500;
    color: #333;
    cursor: pointer;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.days span:hover {
    background-color: #f0f0f0;
}

.days .sunday {
    color: #007bff;
    font-weight: bold;
}

.days .prev-month,
.days .next-month {
    color: #ccc;
    font-weight: normal;
}

.days .prev-month.sunday,
.days .next-month.sunday {
    color: #b3d9ff;
}

/* Responsive design */
@media (max-width: 768px) {
    .calendar-container {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(6, 1fr);
        gap: 15px;
    }
    
    .month {
        padding: 10px;
    }
    
    .month-name {
        font-size: 12px;
    }
    
    .days span {
        font-size: 11px;
        padding: 3px 1px;
    }
}

@media (max-width: 480px) {
    .calendar-container {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(12, 1fr);
        gap: 10px;
    }
    
    .month {
        padding: 8px;
    }
    
    .month-number {
        width: 20px;
        height: 20px;
        font-size: 12px;
    }
    
    .month-name {
        font-size: 11px;
    }
    
    .days-header span {
        font-size: 9px;
    }
    
    .days span {
        font-size: 10px;
        padding: 2px 1px;
    }
}

/* Print styles */
@media print {
    body {
        background-color: white;
        padding: 10px;
    }
    
    .calendar-container {
        gap: 15px;
    }
    
    .month {
        box-shadow: none;
        border: 1px solid #333;
    }
}
